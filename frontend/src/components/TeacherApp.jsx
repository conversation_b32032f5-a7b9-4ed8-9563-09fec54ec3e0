import React, { useState, useEffect } from 'react';
import Header from './Header.jsx';
import Dashboard from './Dashboard.jsx';
import SubjectCreation from './SubjectCreation.jsx';
import SubjectLearning from './SubjectLearning.jsx';
import SettingsModal from './SettingsModal.jsx';
import LanguageSettings from './LanguageSettings.jsx';
import UniversityCourseCreation from './UniversityCourseCreation.jsx';
import { LanguageProvider } from '../contexts/LanguageContext.jsx';
import styles from './TeacherApp.module.css';

function App() {
  const [currentView, setCurrentView] = useState('dashboard'); // 'dashboard', 'create-subject', 'learning', 'create-university-course'
  const [selectedSubject, setSelectedSubject] = useState(null);
  const [apiKey, setApiKey] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const [showLanguageSettings, setShowLanguageSettings] = useState(false);

  useEffect(() => {
    // Load API key from localStorage on component mount
    const savedApiKey = localStorage.getItem('qtmaster_api_key');
    if (savedApiKey) {
      setApiKey(savedApiKey);
    }
  }, []);

  const handleSubjectSelect = (subject) => {
    setSelectedSubject(subject);
    setCurrentView('learning');
  };

  const handleCreateSubject = () => {
    setCurrentView('create-subject');
  };

  const handleCreateUniversityCourse = () => {
    setCurrentView('create-university-course');
  };

  const handleSubjectCreated = (newSubject) => {
    setSelectedSubject(newSubject);
    setCurrentView('learning');
  };

  const handleBackToDashboard = () => {
    setSelectedSubject(null);
    setCurrentView('dashboard');
  };

  const handleSaveApiKey = (key) => {
    setApiKey(key);
    localStorage.setItem('qtmaster_api_key', key);
    setShowSettings(false);
  };

  const renderCurrentView = () => {
    switch (currentView) {
      case 'dashboard':
        return (
          <Dashboard
            onSubjectSelect={handleSubjectSelect}
            onCreateSubject={handleCreateSubject}
            onCreateUniversityCourse={handleCreateUniversityCourse}
          />
        );
      case 'create-subject':
        return (
          <SubjectCreation
            onBack={handleBackToDashboard}
            onSubjectCreated={handleSubjectCreated}
            apiKey={apiKey}
          />
        );
      case 'create-university-course':
        return (
          <UniversityCourseCreation
            onBack={handleBackToDashboard}
            onCourseCreated={handleBackToDashboard}
          />
        );
      case 'learning':
        return (
          <SubjectLearning
            subject={selectedSubject}
            onBack={handleBackToDashboard}
            apiKey={apiKey}
          />
        );
      default:
        return <Dashboard onSubjectSelect={handleSubjectSelect} onCreateSubject={handleCreateSubject} />;
    }
  };

  return (
    <LanguageProvider>
      <div className={styles.app}>
        {currentView === 'dashboard' && (
          <Header
            onSettingsClick={() => setShowSettings(true)}
            onLanguageClick={() => setShowLanguageSettings(true)}
          />
        )}

        {renderCurrentView()}

        {showSettings && (
          <SettingsModal
            onClose={() => setShowSettings(false)}
            onSave={handleSaveApiKey}
            initialApiKey={apiKey}
          />
        )}

        {showLanguageSettings && (
          <LanguageSettings
            isOpen={showLanguageSettings}
            onClose={() => setShowLanguageSettings(false)}
          />
        )}
      </div>
    </LanguageProvider>
  );
}

export default App;
