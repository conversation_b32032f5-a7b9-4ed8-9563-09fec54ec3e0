import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const OAuthCallback = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { login } = useAuth();
  const [status, setStatus] = useState('processing');
  const [error, setError] = useState(null);

  useEffect(() => {
    handleOAuthCallback();
  }, []);

  const handleOAuthCallback = async () => {
    try {
      // Get parameters from URL
      const token = searchParams.get('token');
      const error = searchParams.get('error');
      const provider = searchParams.get('provider');

      if (error) {
        setError(`OAuth authentication failed: ${error}`);
        setStatus('error');
        return;
      }

      if (!token) {
        setError('No authentication token received');
        setStatus('error');
        return;
      }

      // Store the token and user data
      localStorage.setItem('authToken', token);
      
      // Fetch user data from the backend
      const response = await fetch('http://localhost:3001/api/v1/users/profile', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      if (response.ok) {
        const userData = await response.json();
        localStorage.setItem('user', JSON.stringify(userData.data));
        setStatus('success');
        
        // Redirect to dashboard after a short delay
        setTimeout(() => {
          navigate('/dashboard', { replace: true });
        }, 2000);
      } else {
        throw new Error('Failed to fetch user profile');
      }

    } catch (err) {
      console.error('OAuth callback error:', err);
      setError(err.message || 'Authentication failed');
      setStatus('error');
    }
  };

  const renderContent = () => {
    switch (status) {
      case 'processing':
        return (
          <div className="text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-emerald-500 mx-auto mb-6"></div>
            <h2 className="text-2xl font-bold text-white mb-4">Completing Authentication...</h2>
            <p className="text-slate-300">Please wait while we set up your account.</p>
          </div>
        );
      
      case 'success':
        return (
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-emerald-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-white mb-4">Authentication Successful!</h2>
            <p className="text-slate-300 mb-6">Welcome to QtMaster. Redirecting to your dashboard...</p>
            <div className="flex justify-center">
              <div className="animate-pulse bg-emerald-500/20 px-4 py-2 rounded-lg">
                <span className="text-emerald-400 text-sm">Redirecting...</span>
              </div>
            </div>
          </div>
        );
      
      case 'error':
        return (
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-white mb-4">Authentication Failed</h2>
            <p className="text-slate-300 mb-6">{error}</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => navigate('/login')}
                className="bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-200"
              >
                Try Again
              </button>
              <button
                onClick={() => navigate('/')}
                className="border-2 border-slate-600 hover:border-slate-500 text-slate-300 hover:text-white px-6 py-3 rounded-xl font-semibold transition-all duration-200"
              >
                Go Home
              </button>
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-slate-950 flex flex-col justify-center py-12 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 via-transparent to-blue-500/5"></div>
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-emerald-500/10 rounded-full blur-3xl opacity-30"></div>
      <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl opacity-30"></div>
      
      <div className="relative sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center items-center space-x-3 mb-12">
          <div className="w-12 h-12 bg-gradient-to-br from-emerald-400 via-green-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg">
            <span className="text-white font-bold text-2xl">Q</span>
          </div>
          <span className="text-3xl font-bold bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent">QtMaster</span>
        </div>
        
        <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm py-12 px-8 shadow-2xl border border-slate-700/50 rounded-2xl">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default OAuthCallback;
