import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const PricingPage = () => {
  const { isAuthenticated } = useAuth();
  const [isAnnual, setIsAnnual] = useState(false);

  const plans = [
    {
      name: 'Free',
      price: '$0',
      period: 'forever',
      description: 'Perfect for getting started with AI-powered learning',
      features: [
        'Up to 3 subjects',
        'Basic curriculum generation',
        'Progress tracking',
        'Community support',
        'Mobile access',
      ],
      limitations: [
        'Limited to 3 active subjects',
        'Basic AI features only',
        'Standard support',
      ],
      buttonText: 'Get Started Free',
      buttonStyle: 'border-2 border-slate-600 text-slate-300 hover:bg-slate-800 hover:border-emerald-500',
      popular: false,
      icon: '🚀',
    },
    {
      name: 'Pro',
      price: isAnnual ? '$15' : '$19',
      originalPrice: isAnnual ? '$19' : null,
      period: 'per month',
      description: 'Ideal for serious learners and professionals',
      features: [
        'Unlimited subjects & courses',
        'Advanced AI curriculum generation',
        'University-style course creation',
        'Detailed progress analytics',
        'Priority support',
        'Export capabilities',
        'Custom learning paths',
        'Multi-language support',
      ],
      limitations: [],
      buttonText: 'Start Pro Trial',
      buttonStyle: 'bg-gradient-to-r from-emerald-600 to-green-600 text-white hover:from-emerald-700 hover:to-green-700',
      popular: true,
      icon: '⭐',
    },
    {
      name: 'Enterprise',
      price: 'Custom',
      period: 'pricing',
      description: 'For teams and organizations with advanced needs',
      features: [
        'Everything in Pro',
        'Team collaboration',
        'Admin dashboard',
        'Custom integrations',
        'Dedicated support',
        'SSO authentication',
        'Advanced analytics',
        'White-label options',
        'API access',
      ],
      limitations: [],
      buttonText: 'Contact Sales',
      buttonStyle: 'border-2 border-slate-600 text-slate-300 hover:bg-slate-800 hover:border-purple-500',
      popular: false,
      icon: '🏢',
    },
  ];

  return (
    <div className="min-h-screen bg-slate-950">
      {/* Navigation */}
      <nav className="bg-slate-900/80 backdrop-blur-sm border-b border-slate-800 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link to="/" className="flex items-center space-x-3 group">
                <div className="w-10 h-10 bg-gradient-to-br from-emerald-400 via-green-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-emerald-500/25 transition-all duration-300">
                  <span className="text-white font-bold text-xl">Q</span>
                </div>
                <span className="text-xl font-bold bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent">QtMaster</span>
              </Link>
            </div>
            <div className="hidden md:flex items-center space-x-1">
              <Link to="/" className="text-slate-300 hover:text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors hover:bg-slate-800/50">
                Home
              </Link>
              <Link to="/documentation" className="text-slate-300 hover:text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors hover:bg-slate-800/50">
                Documentation
              </Link>
              {isAuthenticated ? (
                <Link to="/dashboard" className="bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 text-white px-6 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-lg hover:shadow-emerald-500/25 ml-4">
                  Dashboard
                </Link>
              ) : (
                <div className="flex items-center space-x-2 ml-4">
                  <Link to="/login" className="text-slate-300 hover:text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors hover:bg-slate-800/50">
                    Sign In
                  </Link>
                  <Link to="/register" className="bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 text-white px-6 py-2 rounded-lg text-sm font-medium transition-all duration-200 shadow-lg hover:shadow-emerald-500/25">
                    Get Started
                  </Link>
                </div>
              )}
            </div>
            {/* Mobile menu button */}
            <div className="md:hidden">
              <button className="text-slate-300 hover:text-white p-2 rounded-lg hover:bg-slate-800/50 transition-colors">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="py-20 lg:py-32 bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/5 via-transparent to-purple-500/5"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="mb-8">
            <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-emerald-500/10 text-emerald-400 border border-emerald-500/20 mb-8">
              💎 Flexible Pricing Plans
            </span>
          </div>

          <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-white mb-8 leading-tight">
            Choose Your Learning Journey
          </h1>
          <p className="text-xl md:text-2xl text-slate-300 mb-12 max-w-4xl mx-auto leading-relaxed">
            Start free and upgrade as you grow. All plans include our core AI-powered curriculum generation.
          </p>

          {/* Pricing Toggle */}
          <div className="flex justify-center items-center space-x-6 mb-16">
            <span className={`text-lg font-medium transition-colors ${!isAnnual ? 'text-white' : 'text-slate-400'}`}>
              Monthly
            </span>
            <button
              onClick={() => setIsAnnual(!isAnnual)}
              className="relative inline-flex h-8 w-16 items-center rounded-full bg-slate-700 transition-colors focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 focus:ring-offset-slate-900"
            >
              <span
                className={`inline-block h-6 w-6 transform rounded-full bg-gradient-to-r from-emerald-500 to-green-500 transition-transform ${
                  isAnnual ? 'translate-x-9' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={`text-lg font-medium transition-colors ${isAnnual ? 'text-white' : 'text-slate-400'}`}>
              Annual
              <span className="ml-3 inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-emerald-600 to-green-600 text-white">
                Save 20%
              </span>
            </span>
          </div>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="pb-24 -mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-3 gap-8 lg:gap-12">
            {plans.map((plan, index) => (
              <div
                key={index}
                className={`relative group ${
                  plan.popular
                    ? 'lg:scale-110 lg:-mt-8 lg:mb-8'
                    : ''
                }`}
              >
                {/* Popular badge */}
                {plan.popular && (
                  <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 z-10">
                    <span className="bg-gradient-to-r from-emerald-500 to-green-500 text-white px-6 py-2 rounded-full text-sm font-semibold shadow-lg">
                      Most Popular
                    </span>
                  </div>
                )}

                <div className={`relative bg-gradient-to-br from-slate-800/50 to-slate-900/50 rounded-3xl p-8 lg:p-10 border transition-all duration-300 ${
                  plan.popular
                    ? 'border-emerald-500/50 shadow-2xl shadow-emerald-500/10'
                    : 'border-slate-700/50 hover:border-slate-600/50 hover:shadow-xl hover:shadow-slate-900/20'
                } group-hover:-translate-y-2`}>

                  {/* Plan header */}
                  <div className="text-center mb-10">
                    <div className="text-4xl mb-4">{plan.icon}</div>
                    <h3 className="text-3xl font-bold text-white mb-3">{plan.name}</h3>
                    <div className="mb-6">
                      <div className="flex items-center justify-center space-x-2">
                        {plan.originalPrice && (
                          <span className="text-2xl text-slate-400 line-through">{plan.originalPrice}</span>
                        )}
                        <span className="text-5xl font-bold text-white">{plan.price}</span>
                      </div>
                      {plan.period !== 'pricing' && (
                        <span className="text-slate-400 text-lg">/{plan.period}</span>
                      )}
                      {isAnnual && plan.name === 'Pro' && (
                        <div className="mt-2">
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-emerald-500/20 text-emerald-400 border border-emerald-500/30">
                            💰 Save $48/year
                          </span>
                        </div>
                      )}
                    </div>
                    <p className="text-slate-300 text-lg leading-relaxed">{plan.description}</p>
                  </div>

                  {/* Features list */}
                  <ul className="space-y-4 mb-10">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start text-slate-300">
                        <svg className="w-6 h-6 text-emerald-500 mr-4 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span className="text-lg">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  {/* CTA Button */}
                  <button className={`w-full py-4 px-6 rounded-xl font-semibold text-lg transition-all duration-300 shadow-lg ${plan.buttonStyle} ${
                    plan.popular ? 'shadow-emerald-500/25 hover:shadow-emerald-500/40' : 'hover:shadow-lg'
                  }`}>
                    {plan.buttonText}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-24 bg-slate-900/50">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-slate-300 max-w-2xl mx-auto">
              Everything you need to know about our pricing and features
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 p-8 rounded-2xl border border-slate-700/50 hover:border-slate-600/50 transition-all duration-300">
              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-gradient-to-br from-emerald-500 to-green-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                  <span className="text-white text-sm">?</span>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-white mb-4">
                    Can I change plans at any time?
                  </h3>
                  <p className="text-slate-300 leading-relaxed">
                    Yes! You can upgrade or downgrade your plan at any time. Changes take effect immediately,
                    and we'll prorate any billing adjustments.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 p-8 rounded-2xl border border-slate-700/50 hover:border-slate-600/50 transition-all duration-300">
              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                  <span className="text-white text-sm">?</span>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-white mb-4">
                    What happens to my data if I downgrade?
                  </h3>
                  <p className="text-slate-300 leading-relaxed">
                    Your data is always safe. If you downgrade, you'll retain access to all your existing
                    content, but you may be limited in creating new subjects based on your plan limits.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 p-8 rounded-2xl border border-slate-700/50 hover:border-slate-600/50 transition-all duration-300">
              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                  <span className="text-white text-sm">?</span>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-white mb-4">
                    Is there a free trial for Pro?
                  </h3>
                  <p className="text-slate-300 leading-relaxed">
                    Yes! We offer a 14-day free trial of our Pro plan. No credit card required to start.
                    You can cancel anytime during the trial period.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 p-8 rounded-2xl border border-slate-700/50 hover:border-slate-600/50 transition-all duration-300">
              <div className="flex items-start space-x-4">
                <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                  <span className="text-white text-sm">?</span>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-white mb-4">
                    How does the AI curriculum generation work?
                  </h3>
                  <p className="text-slate-300 leading-relaxed">
                    Our AI analyzes your learning goals and automatically selects the most appropriate expert
                    persona to create a comprehensive, structured curriculum tailored to your specific needs and skill level.
                  </p>
                </div>
              </div>
            </div>

            <div className="md:col-span-2">
              <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 p-8 rounded-2xl border border-slate-700/50 hover:border-slate-600/50 transition-all duration-300">
                <div className="flex items-start space-x-4">
                  <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-white text-sm">?</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-white mb-4">
                      Do you offer refunds?
                    </h3>
                    <p className="text-slate-300 leading-relaxed">
                      We offer a 30-day money-back guarantee for all paid plans. If you're not satisfied,
                      contact our support team for a full refund. No questions asked.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-br from-emerald-600 via-green-600 to-teal-600 relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-r from-emerald-600/20 via-transparent to-teal-600/20"></div>
        <div className="absolute top-0 left-0 w-96 h-96 bg-white/10 rounded-full blur-3xl opacity-20"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-white/10 rounded-full blur-3xl opacity-20"></div>

        <div className="relative max-w-5xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-8 leading-tight">
            Ready to Accelerate Your Learning?
          </h2>
          <p className="text-xl md:text-2xl text-emerald-100 mb-12 max-w-3xl mx-auto leading-relaxed">
            Join thousands of learners who are already using QtMaster to achieve their goals faster.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <Link to="/register" className="group bg-white text-emerald-600 px-10 py-4 rounded-xl text-lg font-semibold hover:bg-slate-50 transition-all duration-300 shadow-2xl hover:shadow-white/25 transform hover:scale-105">
              <span className="flex items-center">
                Start Free Trial
                <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </span>
            </Link>
            <Link to="/documentation" className="group border-2 border-white/30 hover:border-white text-white hover:bg-white/10 px-10 py-4 rounded-xl text-lg font-semibold transition-all duration-300">
              <span className="flex items-center">
                Learn More
                <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </span>
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900 border-t border-slate-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid md:grid-cols-4 gap-12">
            <div className="md:col-span-2">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-br from-emerald-400 via-green-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg">
                  <span className="text-white font-bold text-xl">Q</span>
                </div>
                <span className="text-2xl font-bold bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent">QtMaster</span>
              </div>
              <p className="text-slate-400 text-lg leading-relaxed max-w-md">
                Empowering learners worldwide with AI-driven personalized education.
                Transform any topic into a comprehensive learning experience.
              </p>
            </div>

            <div>
              <h3 className="text-white font-semibold mb-6 text-lg">Product</h3>
              <ul className="space-y-3">
                <li><Link to="/documentation" className="text-slate-400 hover:text-emerald-400 transition-colors">Documentation</Link></li>
                <li><Link to="/pricing" className="text-slate-400 hover:text-emerald-400 transition-colors">Pricing</Link></li>
                <li><a href="#" className="text-slate-400 hover:text-emerald-400 transition-colors">Features</a></li>
              </ul>
            </div>

            <div>
              <h3 className="text-white font-semibold mb-6 text-lg">Company</h3>
              <ul className="space-y-3">
                <li><a href="#" className="text-slate-400 hover:text-emerald-400 transition-colors">About</a></li>
                <li><a href="#" className="text-slate-400 hover:text-emerald-400 transition-colors">Contact</a></li>
                <li><a href="#" className="text-slate-400 hover:text-emerald-400 transition-colors">Blog</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-slate-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-slate-400 text-sm">
              © 2024 QtMaster. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-slate-400 hover:text-emerald-400 text-sm transition-colors">Privacy Policy</a>
              <a href="#" className="text-slate-400 hover:text-emerald-400 text-sm transition-colors">Terms of Service</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default PricingPage;
