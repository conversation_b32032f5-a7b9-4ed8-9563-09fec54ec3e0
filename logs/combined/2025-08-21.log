{"timestamp":"2025-08-21 17:10:21","level":"warn","message":"DEPRECATION WARNING: utils/logger.ts is deprecated. Use utils/secureLogger.ts directly."}
{"timestamp":"2025-08-21 17:10:21","level":"info","message":"PrismaClient instance created"}
{"timestamp":"2025-08-21 17:10:24","level":"info","message":"Mailjet client initialized successfully"}
{"timestamp":"2025-08-21 17:10:24","level":"info","message":"RBAC system initialized with default permissions"}
{"timestamp":"2025-08-21 17:10:24","level":"info","message":"JWT key rotation service initialization started"}
{"timestamp":"2025-08-21 17:10:24","level":"info","message":"Passport OAuth strategies initialized","meta":{"enabledProviders":["google","facebook","github"],"totalStrategies":3}}
{"timestamp":"2025-08-21 17:10:24","level":"info","message":"Passport OAuth strategies initialized successfully"}
{"timestamp":"2025-08-21 17:10:24","level":"info","message":"🚀 Server running in development mode on port 3001"}
{"timestamp":"2025-08-21 17:10:24","level":"info","message":"📚 API Documentation available at http://localhost:3001/api-docs"}
{"timestamp":"2025-08-21 17:10:24","level":"info","message":"❤️ Health check available at http://localhost:3001/health"}
{"timestamp":"2025-08-21 17:10:24","level":"info","message":"Redis connection established"}
{"timestamp":"2025-08-21 17:10:24","level":"info","message":"Redis connection ready"}
{"timestamp":"2025-08-21 17:10:24","level":"info","message":"JWT key rotation service initialized with existing key","meta":{"keyId":"key_1755676968851_80ffb2e7bf9762c4"}}
{"timestamp":"2025-08-21 17:10:24","level":"info","message":"JWT key rotation scheduled","meta":{"intervalHours":24}}
{"timestamp":"2025-08-21 17:15:55","level":"warn","message":"Suspicious OAuth activity detected","meta":{"indicators":["external_referrer"],"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://localhost:5173/"}}
{"timestamp":"2025-08-21 17:15:55","level":"info","message":"OAuth authentication initiated","meta":{"provider":"google","ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","state":"17557677..."}}
{"timestamp":"2025-08-21 17:15:55","level":"info","message":"GET /api/v1/auth/oauth/google 302 0 - 10.714 ms"}
{"timestamp":"2025-08-21 17:16:02","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":73,"sanitizedLength":78,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/oauth/google/callback"}}
{"timestamp":"2025-08-21 17:16:02","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":116,"sanitizedLength":156,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/oauth/google/callback"}}
{"timestamp":"2025-08-21 17:16:02","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":78,"sanitizedLength":82,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/google/callback"}}
{"timestamp":"2025-08-21 17:16:02","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":156,"sanitizedLength":188,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/google/callback"}}
{"timestamp":"2025-08-21 17:16:02","level":"warn","message":"OAuth state validation failed","meta":{"provider":"google","hasReceivedState":true,"hasSessionState":false,"statesMatch":false,"ip":"::1"}}
{"timestamp":"2025-08-21 17:16:02","level":"info","message":"GET /api/v1/auth/oauth/google/callback?state=1755767755858_iav69344id9_anon&code=4%2F0AVMBsJjUDyTbyTkcnpXHoAx7aVBuAVHjdV_rw7QeI54jFZZjTO5-0WrnhgUfpzvi0_Jy7w&scope=email+profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.profile+openid+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.email&authuser=0&prompt=none 302 100 - 11.933 ms"}
{"timestamp":"2025-08-21 17:25:46","level":"info","message":"Received SIGINT. Starting graceful shutdown..."}
{"timestamp":"2025-08-21 17:25:54","level":"warn","message":"DEPRECATION WARNING: utils/logger.ts is deprecated. Use utils/secureLogger.ts directly."}
{"timestamp":"2025-08-21 17:25:55","level":"info","message":"PrismaClient instance created"}
{"timestamp":"2025-08-21 17:25:57","level":"info","message":"Mailjet client initialized successfully"}
{"timestamp":"2025-08-21 17:25:58","level":"info","message":"RBAC system initialized with default permissions"}
{"timestamp":"2025-08-21 17:25:58","level":"info","message":"JWT key rotation service initialization started"}
{"timestamp":"2025-08-21 17:25:58","level":"info","message":"Passport OAuth strategies initialized","meta":{"enabledProviders":["google","facebook","github"],"totalStrategies":3}}
{"timestamp":"2025-08-21 17:25:58","level":"info","message":"Passport OAuth strategies initialized successfully"}
{"timestamp":"2025-08-21 17:25:58","level":"info","message":"🚀 Server running in development mode on port 3001"}
{"timestamp":"2025-08-21 17:25:58","level":"info","message":"📚 API Documentation available at http://localhost:3001/api-docs"}
{"timestamp":"2025-08-21 17:25:58","level":"info","message":"❤️ Health check available at http://localhost:3001/health"}
{"timestamp":"2025-08-21 17:25:58","level":"info","message":"Redis connection established"}
{"timestamp":"2025-08-21 17:25:58","level":"info","message":"Redis connection ready"}
{"timestamp":"2025-08-21 17:25:58","level":"info","message":"JWT key rotation service initialized with existing key","meta":{"keyId":"key_1755676968851_80ffb2e7bf9762c4"}}
{"timestamp":"2025-08-21 17:25:58","level":"info","message":"JWT key rotation scheduled","meta":{"intervalHours":24}}
{"timestamp":"2025-08-21 17:26:09","level":"info","message":"Response sent","meta":{"method":"GET","path":"/sw.js","statusCode":404,"body":"{\"success\":false,\"message\":\"Route not found\",\"path\":\"/sw.js\"}"}}
{"timestamp":"2025-08-21 17:26:09","level":"info","message":"GET /sw.js 404 61 - 2.824 ms"}
{"timestamp":"2025-08-21 17:26:38","level":"warn","message":"Suspicious OAuth activity detected","meta":{"indicators":["external_referrer"],"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://localhost:5173/"}}
{"timestamp":"2025-08-21 17:26:43","level":"info","message":"GET /api/v1/auth/oauth/google - - - - ms"}
{"timestamp":"2025-08-21 17:26:44","level":"warn","message":"Suspicious OAuth activity detected","meta":{"indicators":["external_referrer"],"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://localhost:5173/"}}
{"timestamp":"2025-08-21 17:26:46","level":"info","message":"Response sent","meta":{"method":"GET","path":"/sw.js","statusCode":404,"body":"{\"success\":false,\"message\":\"Route not found\",\"path\":\"/sw.js\"}"}}
{"timestamp":"2025-08-21 17:26:46","level":"info","message":"GET /sw.js 404 61 - 1.167 ms"}
{"timestamp":"2025-08-21 17:26:49","level":"info","message":"GET /api/v1/auth/oauth/google - - - - ms"}
{"timestamp":"2025-08-21 17:26:52","level":"warn","message":"Suspicious OAuth activity detected","meta":{"indicators":["external_referrer"],"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://localhost:5173/"}}
{"timestamp":"2025-08-21 17:26:53","level":"info","message":"GET /api/v1/auth/oauth/google - - - - ms"}
{"timestamp":"2025-08-21 17:26:53","level":"info","message":"Response sent","meta":{"method":"GET","path":"/sw.js","statusCode":404,"body":"{\"success\":false,\"message\":\"Route not found\",\"path\":\"/sw.js\"}"}}
{"timestamp":"2025-08-21 17:26:53","level":"info","message":"GET /sw.js 404 61 - 0.910 ms"}
{"timestamp":"2025-08-21 17:26:57","level":"warn","message":"Suspicious OAuth activity detected","meta":{"indicators":["external_referrer"],"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://localhost:5173/"}}
{"timestamp":"2025-08-21 17:26:59","level":"info","message":"Response sent","meta":{"method":"GET","path":"/sw.js","statusCode":404,"body":"{\"success\":false,\"message\":\"Route not found\",\"path\":\"/sw.js\"}"}}
{"timestamp":"2025-08-21 17:26:59","level":"info","message":"GET /sw.js 404 61 - 0.660 ms"}
{"timestamp":"2025-08-21 17:28:19","level":"warn","message":"Suspicious OAuth activity detected","meta":{"indicators":["external_referrer"],"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://localhost:5174/"}}
{"timestamp":"2025-08-21 17:29:08","level":"info","message":"GET /api/v1/auth/oauth/google - - - - ms"}
{"timestamp":"2025-08-21 17:29:10","level":"info","message":"GET /api/v1/auth/oauth/google - - - - ms"}
{"timestamp":"2025-08-21 17:29:11","level":"warn","message":"Suspicious OAuth activity detected","meta":{"indicators":["external_referrer"],"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://localhost:5174/"}}
{"timestamp":"2025-08-21 17:29:12","level":"info","message":"GET /api/v1/auth/oauth/google - - - - ms"}
{"timestamp":"2025-08-21 17:29:15","level":"info","message":"Response sent","meta":{"method":"GET","path":"/sw.js","statusCode":404,"body":"{\"success\":false,\"message\":\"Route not found\",\"path\":\"/sw.js\"}"}}
{"timestamp":"2025-08-21 17:29:15","level":"info","message":"GET /sw.js 404 61 - 12.537 ms"}
{"timestamp":"2025-08-21 17:29:20","level":"info","message":"Received SIGINT. Starting graceful shutdown..."}
{"timestamp":"2025-08-21 17:30:40","level":"warn","message":"DEPRECATION WARNING: utils/logger.ts is deprecated. Use utils/secureLogger.ts directly."}
{"timestamp":"2025-08-21 17:30:41","level":"info","message":"PrismaClient instance created"}
{"timestamp":"2025-08-21 17:30:42","level":"info","message":"Mailjet client initialized successfully"}
{"timestamp":"2025-08-21 17:30:43","level":"info","message":"RBAC system initialized with default permissions"}
{"timestamp":"2025-08-21 17:30:43","level":"info","message":"JWT key rotation service initialization started"}
{"timestamp":"2025-08-21 17:30:43","level":"info","message":"Passport OAuth strategies initialized","meta":{"enabledProviders":["google","facebook","github"],"totalStrategies":3}}
{"timestamp":"2025-08-21 17:30:43","level":"info","message":"Passport OAuth strategies initialized successfully"}
{"timestamp":"2025-08-21 17:30:43","level":"info","message":"🚀 Server running in development mode on port 3001"}
{"timestamp":"2025-08-21 17:30:43","level":"info","message":"📚 API Documentation available at http://localhost:3001/api-docs"}
{"timestamp":"2025-08-21 17:30:43","level":"info","message":"❤️ Health check available at http://localhost:3001/health"}
{"timestamp":"2025-08-21 17:30:43","level":"info","message":"Redis connection established"}
{"timestamp":"2025-08-21 17:30:43","level":"info","message":"Redis connection ready"}
{"timestamp":"2025-08-21 17:30:43","level":"info","message":"JWT key rotation service initialized with existing key","meta":{"keyId":"key_1755676968851_80ffb2e7bf9762c4"}}
{"timestamp":"2025-08-21 17:30:43","level":"info","message":"JWT key rotation scheduled","meta":{"intervalHours":24}}
{"timestamp":"2025-08-21 17:31:01","level":"warn","message":"Suspicious OAuth activity detected","meta":{"indicators":["external_referrer"],"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://localhost:5174/"}}
{"timestamp":"2025-08-21 17:31:01","level":"info","message":"OAuth authentication initiated","meta":{"provider":"google","ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","state":"17557686..."}}
{"timestamp":"2025-08-21 17:31:01","level":"info","message":"GET /api/v1/auth/oauth/google 302 0 - 6.631 ms"}
{"timestamp":"2025-08-21 17:31:06","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":73,"sanitizedLength":78,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/oauth/google/callback"}}
{"timestamp":"2025-08-21 17:31:06","level":"warn","message":"Potentially malicious input detected and sanitized","meta":{"originalLength":116,"sanitizedLength":156,"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","path":"/oauth/google/callback"}}
{"timestamp":"2025-08-21 17:31:06","level":"error","message":"Passport authentication failed","meta":{"provider":"google","error":{"name":"TokenError","message":"Malformed auth code.","stack":"TokenError: Malformed auth code.\n    at OAuth2Strategy.parseErrorResponse (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/passport-oauth2/lib/strategy.js:373:12)\n    at OAuth2Strategy._createOAuthError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/passport-oauth2/lib/strategy.js:420:16)\n    at /Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/passport-oauth2/lib/strategy.js:177:45\n    at /Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/oauth/lib/oauth2.js:196:18\n    at passBackControl (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/oauth/lib/oauth2.js:132:9)\n    at IncomingMessage.<anonymous> (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/oauth/lib/oauth2.js:157:7)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)"},"ip":"::1"}}
{"timestamp":"2025-08-21 17:31:06","level":"info","message":"GET /api/v1/auth/oauth/google/callback?state=1755768661085_l9p15ewbn2p_anon&code=4%2F0AVMBsJiA0mJjB4I49L7k3l-awXBMO8VXnHADQcqMrubFr4NN8y4wdy_HbMXH9GVNDjk_Yw&scope=email+profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.email+openid&authuser=0&prompt=none 302 97 - 452.193 ms"}
{"timestamp":"2025-08-21 17:34:51","level":"info","message":"Received SIGINT. Starting graceful shutdown..."}
{"timestamp":"2025-08-21 17:34:59","level":"warn","message":"DEPRECATION WARNING: utils/logger.ts is deprecated. Use utils/secureLogger.ts directly."}
{"timestamp":"2025-08-21 17:34:59","level":"info","message":"PrismaClient instance created"}
{"timestamp":"2025-08-21 17:35:01","level":"info","message":"Mailjet client initialized successfully"}
{"timestamp":"2025-08-21 17:35:02","level":"info","message":"RBAC system initialized with default permissions"}
{"timestamp":"2025-08-21 17:35:02","level":"info","message":"JWT key rotation service initialization started"}
{"timestamp":"2025-08-21 17:35:02","level":"info","message":"Passport OAuth strategies initialized","meta":{"enabledProviders":["google","facebook","github"],"totalStrategies":3}}
{"timestamp":"2025-08-21 17:35:02","level":"info","message":"Passport OAuth strategies initialized successfully"}
{"timestamp":"2025-08-21 17:35:02","level":"info","message":"🚀 Server running in development mode on port 3001"}
{"timestamp":"2025-08-21 17:35:02","level":"info","message":"📚 API Documentation available at http://localhost:3001/api-docs"}
{"timestamp":"2025-08-21 17:35:02","level":"info","message":"❤️ Health check available at http://localhost:3001/health"}
{"timestamp":"2025-08-21 17:35:02","level":"info","message":"Redis connection established"}
{"timestamp":"2025-08-21 17:35:02","level":"info","message":"Redis connection ready"}
{"timestamp":"2025-08-21 17:35:02","level":"info","message":"JWT key rotation service initialized with existing key","meta":{"keyId":"key_1755676968851_80ffb2e7bf9762c4"}}
{"timestamp":"2025-08-21 17:35:02","level":"info","message":"JWT key rotation scheduled","meta":{"intervalHours":24}}
{"timestamp":"2025-08-21 17:35:41","level":"warn","message":"Suspicious OAuth activity detected","meta":{"indicators":["external_referrer"],"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://localhost:5174/"}}
{"timestamp":"2025-08-21 17:35:48","level":"info","message":"GET /api/v1/auth/oauth/google - - - - ms"}
{"timestamp":"2025-08-21 17:35:49","level":"warn","message":"Suspicious OAuth activity detected","meta":{"indicators":["external_referrer"],"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://localhost:5174/"}}
{"timestamp":"2025-08-21 17:36:08","level":"info","message":"GET /api/v1/auth/oauth/google - - - - ms"}
{"timestamp":"2025-08-21 17:36:15","level":"warn","message":"DEPRECATION WARNING: utils/logger.ts is deprecated. Use utils/secureLogger.ts directly."}
{"timestamp":"2025-08-21 17:36:15","level":"info","message":"PrismaClient instance created"}
{"timestamp":"2025-08-21 17:36:18","level":"info","message":"Mailjet client initialized successfully"}
{"timestamp":"2025-08-21 17:36:18","level":"info","message":"RBAC system initialized with default permissions"}
{"timestamp":"2025-08-21 17:36:18","level":"info","message":"JWT key rotation service initialization started"}
{"timestamp":"2025-08-21 17:36:18","level":"info","message":"Passport OAuth strategies initialized","meta":{"enabledProviders":["google","facebook","github"],"totalStrategies":3}}
{"timestamp":"2025-08-21 17:36:18","level":"info","message":"Passport OAuth strategies initialized successfully"}
{"timestamp":"2025-08-21 17:36:18","level":"info","message":"🚀 Server running in development mode on port 3001"}
{"timestamp":"2025-08-21 17:36:18","level":"info","message":"📚 API Documentation available at http://localhost:3001/api-docs"}
{"timestamp":"2025-08-21 17:36:18","level":"info","message":"❤️ Health check available at http://localhost:3001/health"}
{"timestamp":"2025-08-21 17:36:18","level":"info","message":"Redis connection established"}
{"timestamp":"2025-08-21 17:36:18","level":"info","message":"Redis connection ready"}
{"timestamp":"2025-08-21 17:36:18","level":"info","message":"JWT key rotation service initialized with existing key","meta":{"keyId":"key_1755676968851_80ffb2e7bf9762c4"}}
{"timestamp":"2025-08-21 17:36:18","level":"info","message":"JWT key rotation scheduled","meta":{"intervalHours":24}}
{"timestamp":"2025-08-21 17:36:21","level":"warn","message":"Suspicious OAuth activity detected","meta":{"indicators":["external_referrer"],"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://localhost:5174/"}}
{"timestamp":"2025-08-21 17:36:49","level":"info","message":"GET /api/v1/auth/oauth/google - - - - ms"}
{"timestamp":"2025-08-21 17:36:50","level":"info","message":"Response sent","meta":{"method":"GET","path":"/sw.js","statusCode":404,"body":"{\"success\":false,\"message\":\"Route not found\",\"path\":\"/sw.js\"}"}}
{"timestamp":"2025-08-21 17:36:50","level":"info","message":"GET /sw.js 404 61 - 3.341 ms"}
{"timestamp":"2025-08-21 17:36:55","level":"warn","message":"Suspicious OAuth activity detected","meta":{"indicators":["external_referrer"],"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://localhost:5174/"}}
{"timestamp":"2025-08-21 17:37:16","level":"info","message":"GET /api/v1/auth/oauth/google - - - - ms"}
{"timestamp":"2025-08-21 17:37:18","level":"warn","message":"Suspicious OAuth activity detected","meta":{"indicators":["external_referrer"],"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://localhost:5174/"}}
{"timestamp":"2025-08-21 17:37:19","level":"info","message":"Response sent","meta":{"method":"GET","path":"/sw.js","statusCode":404,"body":"{\"success\":false,\"message\":\"Route not found\",\"path\":\"/sw.js\"}"}}
{"timestamp":"2025-08-21 17:37:19","level":"info","message":"GET /sw.js 404 61 - 1.580 ms"}
{"timestamp":"2025-08-21 17:37:57","level":"info","message":"Received SIGINT. Starting graceful shutdown..."}
{"timestamp":"2025-08-21 17:38:06","level":"warn","message":"DEPRECATION WARNING: utils/logger.ts is deprecated. Use utils/secureLogger.ts directly."}
{"timestamp":"2025-08-21 17:38:06","level":"info","message":"PrismaClient instance created"}
{"timestamp":"2025-08-21 17:38:07","level":"info","message":"Mailjet client initialized successfully"}
{"timestamp":"2025-08-21 17:38:07","level":"info","message":"RBAC system initialized with default permissions"}
{"timestamp":"2025-08-21 17:38:07","level":"info","message":"JWT key rotation service initialization started"}
{"timestamp":"2025-08-21 17:38:07","level":"info","message":"Passport OAuth strategies initialized","meta":{"enabledProviders":["google","facebook","github"],"totalStrategies":3}}
{"timestamp":"2025-08-21 17:38:07","level":"info","message":"Passport OAuth strategies initialized successfully"}
{"timestamp":"2025-08-21 17:38:07","level":"info","message":"🚀 Server running in development mode on port 3001"}
{"timestamp":"2025-08-21 17:38:07","level":"info","message":"📚 API Documentation available at http://localhost:3001/api-docs"}
{"timestamp":"2025-08-21 17:38:07","level":"info","message":"❤️ Health check available at http://localhost:3001/health"}
{"timestamp":"2025-08-21 17:38:07","level":"info","message":"Redis connection established"}
{"timestamp":"2025-08-21 17:38:07","level":"info","message":"Redis connection ready"}
{"timestamp":"2025-08-21 17:38:07","level":"info","message":"JWT key rotation service initialized with existing key","meta":{"keyId":"key_1755676968851_80ffb2e7bf9762c4"}}
{"timestamp":"2025-08-21 17:38:07","level":"info","message":"JWT key rotation scheduled","meta":{"intervalHours":24}}
{"timestamp":"2025-08-21 17:38:22","level":"warn","message":"Suspicious OAuth activity detected","meta":{"indicators":["external_referrer"],"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://localhost:5174/"}}
{"timestamp":"2025-08-21 17:38:50","level":"info","message":"GET /api/v1/auth/oauth/google - - - - ms"}
{"timestamp":"2025-08-21 17:38:51","level":"warn","message":"Suspicious OAuth activity detected","meta":{"indicators":["external_referrer"],"ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","referer":"http://localhost:5174/"}}
{"timestamp":"2025-08-21 17:38:52","level":"info","message":"Response sent","meta":{"method":"GET","path":"/sw.js","statusCode":404,"body":"{\"success\":false,\"message\":\"Route not found\",\"path\":\"/sw.js\"}"}}
{"timestamp":"2025-08-21 17:38:52","level":"info","message":"GET /sw.js 404 61 - 2.590 ms"}
{"timestamp":"2025-08-21 17:39:54","level":"info","message":"OAuth authentication initiated","meta":{"provider":"google","ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","state":"17557691..."}}
{"timestamp":"2025-08-21 17:40:38","level":"info","message":"OAuth authentication initiated","meta":{"provider":"google","ip":"::1","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","state":"17557692..."}}
{"timestamp":"2025-08-21 17:40:38","level":"info","message":"GET /api/v1/auth/oauth/google 302 0 - 107455.348 ms"}
{"timestamp":"2025-08-21 17:42:06","level":"info","message":"Received SIGTERM. Starting graceful shutdown..."}
{"timestamp":"2025-08-21 17:42:23","level":"warn","message":"DEPRECATION WARNING: utils/logger.ts is deprecated. Use utils/secureLogger.ts directly."}
{"timestamp":"2025-08-21 17:42:23","level":"info","message":"PrismaClient instance created"}
{"timestamp":"2025-08-21 17:42:25","level":"info","message":"Mailjet client initialized successfully"}
{"timestamp":"2025-08-21 17:42:26","level":"info","message":"RBAC system initialized with default permissions"}
{"timestamp":"2025-08-21 17:42:26","level":"info","message":"JWT key rotation service initialization started"}
{"timestamp":"2025-08-21 17:42:26","level":"info","message":"Passport OAuth strategies initialized","meta":{"enabledProviders":["google","facebook","github"],"totalStrategies":3}}
{"timestamp":"2025-08-21 17:42:26","level":"info","message":"Passport OAuth strategies initialized successfully"}
{"timestamp":"2025-08-21 17:42:26","level":"info","message":"🚀 Server running in development mode on port 3001"}
{"timestamp":"2025-08-21 17:42:26","level":"info","message":"📚 API Documentation available at http://localhost:3001/api-docs"}
{"timestamp":"2025-08-21 17:42:26","level":"info","message":"❤️ Health check available at http://localhost:3001/health"}
{"timestamp":"2025-08-21 17:42:26","level":"info","message":"Redis connection established"}
{"timestamp":"2025-08-21 17:42:26","level":"info","message":"Redis connection ready"}
{"timestamp":"2025-08-21 17:42:26","level":"info","message":"JWT key rotation service initialized with existing key","meta":{"keyId":"key_1755676968851_80ffb2e7bf9762c4"}}
{"timestamp":"2025-08-21 17:42:26","level":"info","message":"JWT key rotation scheduled","meta":{"intervalHours":24}}
