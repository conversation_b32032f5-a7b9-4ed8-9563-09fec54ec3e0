{"timestamp": "2025-08-21 17:31:06", "level": "error", "message": "Passport authentication failed", "meta": {"provider": "google", "error": {"name": "TokenError", "message": "Malformed auth code.", "stack": "TokenError: Malformed auth code.\n    at OAuth2Strategy.parseErrorResponse (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/passport-oauth2/lib/strategy.js:373:12)\n    at OAuth2Strategy._createOAuthError (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/passport-oauth2/lib/strategy.js:420:16)\n    at /Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/passport-oauth2/lib/strategy.js:177:45\n    at /Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/oauth/lib/oauth2.js:196:18\n    at passBackControl (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/oauth/lib/oauth2.js:132:9)\n    at IncomingMessage.<anonymous> (/Users/<USER>/Downloads/QtMaster.io/secure_backend/node_modules/oauth/lib/oauth2.js:157:7)\n    at IncomingMessage.emit (node:events:530:35)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)"}, "ip": "::1"}}