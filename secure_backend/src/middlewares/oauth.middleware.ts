import { Request, Response, NextFunction } from 'express';
import { RateLimiterRedis } from 'rate-limiter-flexible';
import { HttpException } from '../exceptions/HttpException';
import { logger } from '../utils/secureLogger';
import { sanitizeError } from '../utils/logSanitizer';
import { OAuthConfig, OAuthProvider, OAuthStateManager } from '../config/oauth.config';
import { redis } from '../config/redis';
import { User as UserInterface } from '../interfaces/user.interface';
import { ORIGIN } from '../config';

/**
 * OAuth-specific rate limiters
 */
const oauthInitiateRateLimit = new RateLimiterRedis({
  storeClient: redis,
  keyPrefix: 'rl_oauth_initiate',
  points: 10, // Number of OAuth initiation attempts
  duration: 15 * 60, // Per 15 minutes
  blockDuration: 15 * 60, // Block for 15 minutes
  execEvenly: true,
});

const oauthCallbackRateLimit = new RateLimiterRedis({
  storeClient: redis,
  keyPrefix: 'rl_oauth_callback',
  points: 20, // Number of callback attempts
  duration: 15 * 60, // Per 15 minutes
  blockDuration: 15 * 60, // Block for 15 minutes
  execEvenly: true,
});

const oauthLinkingRateLimit = new RateLimiterRedis({
  storeClient: redis,
  keyPrefix: 'rl_oauth_linking',
  points: 5, // Number of account linking attempts
  duration: 60 * 60, // Per hour
  blockDuration: 60 * 60, // Block for 1 hour
  execEvenly: true,
});

/**
 * OAuth Security Middleware
 */
export class OAuthSecurityMiddleware {
  /**
   * Validate OAuth provider parameter
   */
  static validateProvider = (req: Request, _res: Response, next: NextFunction): void => {
    try {
      const provider = req.params['provider'] as OAuthProvider;

      if (!provider) {
        throw new HttpException(400, 'OAuth provider is required');
      }

      if (!['google', 'facebook', 'github'].includes(provider)) {
        throw new HttpException(400, `OAuth provider '${provider}' is not enabled or configured`);
      }

      if (!OAuthConfig.isProviderEnabled(provider)) {
        throw new HttpException(400, `OAuth provider '${provider}' is not enabled or configured`);
      }

      next();
    } catch (error) {
      logger.error('OAuth provider validation failed', {
        provider: req.params['provider'],
        error: sanitizeError(error),
        ip: req.ip,
      });
      next(error);
    }
  };

  /**
   * Generate and validate OAuth state parameter for CSRF protection
   */
  static generateOAuthState = (req: Request, _res: Response, next: NextFunction): void => {
    try {
      // Generate secure state parameter
      const userId = (req.user as UserInterface)?.id;
      const state = OAuthStateManager.generateState(userId);

      // Store state in session for validation
      if (req.session) {
        req.session.oauthState = state;
        req.session.oauthStateTimestamp = Date.now();
      }

      // Attach state to request for use in OAuth URL
      (req as any).oauthState = state;

      logger.debug('OAuth state generated', {
        statePrefix: state.substring(0, 8) + '...',
        userId: userId?.substring(0, 8) + '...' || 'anonymous',
        ip: req.ip,
      });

      next();
    } catch (error) {
      logger.error('Failed to generate OAuth state', {
        error: sanitizeError(error),
        ip: req.ip,
      });
      next(new HttpException(500, 'Failed to generate OAuth state'));
    }
  };

  /**
   * Validate OAuth state parameter in callback
   */
  static validateOAuthState = (req: Request, _res: Response, next: NextFunction): void => {
    try {
      const receivedState = req.query['state'] as string;
      const sessionState = req.session?.oauthState;
      const stateTimestamp = req.session?.oauthStateTimestamp;

      // Check if state parameters exist
      if (!receivedState || !sessionState) {
        logger.warn('OAuth callback missing state parameter', {
          hasReceivedState: !!receivedState,
          hasSessionState: !!sessionState,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
        });
        throw new HttpException(400, 'Invalid OAuth state parameter');
      }

      // Validate state format and expiration
      if (!OAuthStateManager.validateState(receivedState)) {
        logger.warn('OAuth state parameter validation failed', {
          ip: req.ip,
          userAgent: req.get('User-Agent'),
        });
        throw new HttpException(400, 'Invalid or expired OAuth state parameter');
      }

      // Check state parameter match
      if (receivedState !== sessionState) {
        logger.warn('OAuth state parameter mismatch', {
          ip: req.ip,
          userAgent: req.get('User-Agent'),
        });
        throw new HttpException(400, 'OAuth state parameter mismatch');
      }

      // Check state timestamp (additional protection)
      if (stateTimestamp) {
        const stateAge = Date.now() - stateTimestamp;
        const maxAge = 10 * 60 * 1000; // 10 minutes

        if (stateAge > maxAge) {
          logger.warn('OAuth state parameter expired', {
            stateAge: Math.floor(stateAge / 1000) + 's',
            ip: req.ip,
          });
          throw new HttpException(400, 'OAuth state parameter expired');
        }
      }

      // Clear state from session after successful validation
      delete req.session.oauthState;
      delete req.session.oauthStateTimestamp;

      next();
    } catch (error) {
      // Clear state from session on validation failure
      if (req.session) {
        delete req.session.oauthState;
        delete req.session.oauthStateTimestamp;
      }

      logger.error('OAuth state validation failed', {
        error: sanitizeError(error),
        ip: req.ip,
      });
      next(error);
    }
  };

  /**
   * Rate limiting for OAuth initiation
   */
  static oauthInitiateRateLimit = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const key = req.ip || 'unknown';
      await oauthInitiateRateLimit.consume(key);
      next();
    } catch (rateLimiterRes) {
      const rateLimiterError = rateLimiterRes as { remainingPoints?: number; msBeforeNext?: number };
      const remainingPoints = rateLimiterError?.remainingPoints || 0;
      const msBeforeNext = rateLimiterError?.msBeforeNext || 0;

      logger.warn('OAuth initiation rate limit exceeded', {
        ip: req.ip,
        remainingPoints,
        msBeforeNext,
        provider: req.params['provider'],
      });

      res.set({
        'Retry-After': Math.round(msBeforeNext / 1000) || 1,
        'X-RateLimit-Limit': oauthInitiateRateLimit.points,
        'X-RateLimit-Remaining': remainingPoints,
        'X-RateLimit-Reset': new Date(Date.now() + msBeforeNext).toISOString(),
      });

      next(new HttpException(429, 'Too many OAuth initiation attempts. Please try again later.'));
    }
  };

  /**
   * Rate limiting for OAuth callbacks
   */
  static oauthCallbackRateLimit = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const key = req.ip || 'unknown';
      await oauthCallbackRateLimit.consume(key);
      next();
    } catch (rateLimiterRes) {
      const rateLimiterError = rateLimiterRes as { remainingPoints?: number; msBeforeNext?: number };
      const remainingPoints = rateLimiterError?.remainingPoints || 0;
      const msBeforeNext = rateLimiterError?.msBeforeNext || 0;

      logger.warn('OAuth callback rate limit exceeded', {
        ip: req.ip,
        remainingPoints,
        msBeforeNext,
        provider: req.params['provider'],
      });

      res.set({
        'Retry-After': Math.round(msBeforeNext / 1000) || 1,
        'X-RateLimit-Limit': oauthCallbackRateLimit.points,
        'X-RateLimit-Remaining': remainingPoints,
        'X-RateLimit-Reset': new Date(Date.now() + msBeforeNext).toISOString(),
      });

      // For callbacks, redirect to failure URL instead of JSON error
      const failureUrl = OAuthConfig.getRedirectUrls().failure + '&error=rate_limit_exceeded';
      res.redirect(failureUrl);
    }
  };

  /**
   * Rate limiting for OAuth account linking
   */
  static oauthLinkingRateLimit = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const key = (req.user as UserInterface)?.id || req.ip || 'unknown';
      await oauthLinkingRateLimit.consume(key);
      next();
    } catch (rateLimiterRes) {
      const remainingPoints = (rateLimiterRes as any)?.remainingPoints || 0;
      const msBeforeNext = (rateLimiterRes as any)?.msBeforeNext || 0;

      logger.warn('OAuth linking rate limit exceeded', {
        userId: (req.user as any)?.id,
        ip: req.ip,
        remainingPoints,
        msBeforeNext,
        provider: req.params['provider'],
      });

      res.set({
        'Retry-After': Math.round(msBeforeNext / 1000) || 1,
        'X-RateLimit-Limit': oauthLinkingRateLimit.points,
        'X-RateLimit-Remaining': remainingPoints,
        'X-RateLimit-Reset': new Date(Date.now() + msBeforeNext).toISOString(),
      });

      next(new HttpException(429, 'Too many account linking attempts. Please try again later.'));
    }
  };

  /**
   * Validate OAuth callback query parameters
   */
  static validateCallbackParams = (_req: Request, _res: Response, next: NextFunction): void => {
    // For now, skip validation as the existing validation middleware doesn't support query params
    // TODO: Implement proper query parameter validation
    next();
  };

  /**
   * Validate OAuth provider parameter
   */
  static validateProviderParam = (_req: Request, _res: Response, next: NextFunction): void => {
    // For now, skip validation as the existing validation middleware doesn't support params
    // TODO: Implement proper parameter validation
    next();
  };

  /**
   * Detect suspicious OAuth activity
   */
  static detectSuspiciousOAuthActivity = (req: Request, _res: Response, next: NextFunction): void => {
    try {
      const suspiciousIndicators: string[] = [];

      // Check for missing or suspicious User-Agent
      const userAgent = req.get('User-Agent');
      if (!userAgent || userAgent.length < 10) {
        suspiciousIndicators.push('missing_or_short_user_agent');
      }

      // Check for suspicious referrer patterns
      const referer = req.get('Referer');
      if (referer) {
        // Define trusted frontend origins for OAuth flows
        const trustedOrigins = [
          'http://localhost:5173', // Default Vite dev server
          'http://localhost:5174', // Alternative Vite dev server port
          'http://localhost:3000', // Next.js dev server
          'http://localhost:3001', // Backend server (for testing)
          ORIGIN === '*' ? null : ORIGIN, // Dynamic CORS origin from config (skip if wildcard)
        ].filter(Boolean); // Remove undefined/null values

        // Check if referrer is from a trusted origin
        const isTrustedReferrer = trustedOrigins.some(origin =>
          referer.startsWith(origin + '/') || referer === origin
        );

        if (!isTrustedReferrer) {
          suspiciousIndicators.push('external_referrer');
        }
      }

      // Check for rapid successive requests (basic check)
      const deviceFingerprint = (req as any).deviceFingerprint;
      if (!deviceFingerprint) {
        suspiciousIndicators.push('missing_device_fingerprint');
      }

      // Log suspicious activity but don't block (for monitoring)
      if (suspiciousIndicators.length > 0) {
        logger.warn('Suspicious OAuth activity detected', {
          indicators: suspiciousIndicators,
          ip: req.ip,
          userAgent,
          referer,
          provider: req.params['provider'],
        });
      }

      next();
    } catch (error) {
      logger.error('OAuth suspicious activity detection failed', {
        error: sanitizeError(error),
        ip: req.ip,
      });
      // Don't block on detection errors
      next();
    }
  };

  /**
   * Validate OAuth session requirements
   */
  static validateOAuthSession = (req: Request, _res: Response, next: NextFunction): void => {
    try {
      // Ensure session exists for OAuth flows
      if (!req.session) {
        logger.error('OAuth request without session', {
          ip: req.ip,
          provider: req.params['provider'],
        });
        throw new HttpException(500, 'Session required for OAuth authentication');
      }

      // Check session integrity
      if (!req.sessionID) {
        logger.error('OAuth request with invalid session', {
          ip: req.ip,
          provider: req.params['provider'],
        });
        throw new HttpException(500, 'Invalid session for OAuth authentication');
      }

      next();
    } catch (error) {
      logger.error('OAuth session validation failed', {
        error: sanitizeError(error),
        ip: req.ip,
      });
      next(error);
    }
  };
}

// Export individual middleware functions for easier use
export const {
  validateProvider,
  generateOAuthState,
  validateOAuthState,
  validateCallbackParams,
  validateProviderParam,
  detectSuspiciousOAuthActivity,
  validateOAuthSession,
} = OAuthSecurityMiddleware;

// Export rate limiter middleware functions as wrapper functions
export const oauthInitiateRateLimitMiddleware = OAuthSecurityMiddleware.oauthInitiateRateLimit;
export const oauthCallbackRateLimitMiddleware = OAuthSecurityMiddleware.oauthCallbackRateLimit;
export const oauthLinkingRateLimitMiddleware = OAuthSecurityMiddleware.oauthLinkingRateLimit;

export default OAuthSecurityMiddleware;
